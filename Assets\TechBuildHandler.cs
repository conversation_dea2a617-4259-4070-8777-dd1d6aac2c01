using UnityEngine;
using System.Collections.Generic;

public class TechBuildHandler : MonoBehaviour
{
    public static TechBuildHandler Instance
    {
        get
        {
            if (_instance == null)
            {
                GameObject go = new GameObject("TechBuildHandler");
                _instance = go.AddComponent<TechBuildHandler>();
            }
            return _instance;
        }
        private set { _instance = value; }
    }
    private static TechBuildHandler _instance;
    public static bool IsBuildModeActive => Instance != null && Instance.currentState == BuildSelectionState.Active;
    private static bool suppressNextCardClick = false;

    private enum BuildSelectionState
    {
        Inactive,
        Active
    }
    
    private BuildSelectionState currentState = BuildSelectionState.Inactive;
    private CardData techToBuild;
    private List<GameObject> glowingWorlds = new List<GameObject>();
    private List<GameObject> glowingWorldCards = new List<GameObject>();
    
    private void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this;
    }
    
    private void Update()
    {
        if (currentState == BuildSelectionState.Active && Input.GetMouseButtonDown(0))
        {
            HandleBuildModeClick();
        }
    }

    public static void SuppressNextCardClick()
    {
        suppressNextCardClick = true;
    }

    public static bool ShouldSuppressCardClick()
    {
        if (suppressNextCardClick)
        {
            suppressNextCardClick = false;
            return true;
        }
        return false;
    }

    public void OnBuildButtonClicked(CardData cardData)
    {
        techToBuild = cardData;
        EnterBuildSelectionMode();
    }

    private void EnterBuildSelectionMode()
    {
        currentState = BuildSelectionState.Active;

        // Hide detail panel
        CardDetailDisplay detailDisplay = CardDetailDisplay.Instance;
        if (detailDisplay != null)
        {
            detailDisplay.Hide();
        }

        // Show activation instructions
        GameUI gameUI = FindFirstObjectByType<GameUI>();
        if (gameUI != null && techToBuild != null)
        {
            gameUI.ShowActivationInstructions($"{techToBuild.Name}: Pick world to build");
        }

        // Highlight worlds with appropriate assemblers
        HighlightBuildableWorlds();
    }

    private void ExitBuildSelectionMode()
    {
        currentState = BuildSelectionState.Inactive;

        // Stop all glowing on world cards
        foreach (GameObject worldCard in glowingWorldCards)
        {
            if (worldCard != null)
            {
                BuildModeGlowEffect glowEffect = worldCard.GetComponent<BuildModeGlowEffect>();
                if (glowEffect != null)
                {
                    glowEffect.SetBuildModeGlow(false);
                }
            }
        }
        glowingWorldCards.Clear();

        // Stop all glowing on celestial bodies
        foreach (GameObject world in glowingWorlds)
        {
            if (world != null)
            {
                BuildModeGlowEffect glowEffect = world.GetComponent<BuildModeGlowEffect>();
                if (glowEffect != null)
                {
                    glowEffect.SetBuildModeGlow(false);
                }
            }
        }
        glowingWorlds.Clear();

        techToBuild = null;

        // Hide activation instructions
        GameUI gameUI = FindFirstObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.HideActivationInstructions();
        }
    }

    private void HighlightBuildableWorlds()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager?.CurrentPlayer == null || techToBuild == null)
        {
            Debug.LogError($"[TechBuildHandler] Missing requirements - GameManager: {gameManager != null}, CurrentPlayer: {gameManager?.CurrentPlayer != null}, TechToBuild: {techToBuild != null}");
            return;
        }

        Player currentPlayer = gameManager.CurrentPlayer;
        // Get all world cards from current player's play area
        PlayAreaManager areaManager = PlayAreaManager.Instance;
        if (areaManager != null)
        {
            PlayerPlayArea playArea = areaManager.GetPlayerPlayArea(currentPlayer.PlayerId);
            if (playArea != null)
            {
                List<GameObject> worldCards = playArea.GetWorldCards();
                foreach (GameObject worldCard in worldCards)
                {
                    WorldCardVisual worldVisual = worldCard.GetComponent<WorldCardVisual>();
                    if (worldVisual != null)
                    {
                        GameObject celestialBody = worldVisual.GetCelestialBody();
                        if (celestialBody != null)
                        {
                            bool canBuild = CanBuildTechAtWorld(currentPlayer, celestialBody);

                            if (canBuild)
                            {
                                // Make world card glow
                                BuildModeGlowEffect cardGlowEffect = worldCard.GetComponent<BuildModeGlowEffect>();
                                if (cardGlowEffect == null)
                                {
                                    cardGlowEffect = worldCard.AddComponent<BuildModeGlowEffect>();
                                }
                                cardGlowEffect.SetBuildModeGlow(true);
                                glowingWorldCards.Add(worldCard);

                                // Make celestial body glow
                                BuildModeGlowEffect worldGlowEffect = celestialBody.GetComponent<BuildModeGlowEffect>();
                                if (worldGlowEffect == null)
                                {
                                    worldGlowEffect = celestialBody.AddComponent<BuildModeGlowEffect>();
                                }
                                worldGlowEffect.SetBuildModeGlow(true);
                                glowingWorlds.Add(celestialBody);
                            }
                        }
                        else
                        {
                            Debug.LogWarning($"[TechBuildHandler] World card {worldCard.name} has no celestial body reference");
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"[TechBuildHandler] World card {worldCard.name} has no WorldCardVisual component");
                    }
                }
            }
            else
            {
                Debug.LogError($"[TechBuildHandler] No play area found for player {currentPlayer.PlayerId}");
            }
        }
        else
        {
            Debug.LogError("[TechBuildHandler] PlayAreaManager.Instance is null");
        }
    }

    private bool CanBuildTechAtWorld(Player player, GameObject world)
    {
        if (techToBuild == null || player == null || world == null)
        {
            Debug.LogWarning($"[TechBuildHandler] Null check failed - TechToBuild: {techToBuild != null}, Player: {player != null}, World: {world != null}");
            return false;
        }

        // Check if player has required resources at this location
        bool hasResources = HasRequiredBuildResources(player, world);
        if (!hasResources)
            return false;

        // Check assembler requirements based on tech type
        string subType = techToBuild.SubType?.ToLower() ?? "";
        if (subType.Contains("module"))
        {
            // Modules need basic assembler
            bool hasBasic = HasBasicAssembler(player, world);
            bool hasAdvanced = HasAdvancedAssembler(player, world);
            return hasBasic || hasAdvanced;
        }
        else if (subType.Contains("facility") || subType.Contains("ship"))
        {
            // Facilities and ships need advanced assembler
            bool hasAdvanced = HasAdvancedAssembler(player, world);
            return hasAdvanced;
        }

        Debug.LogWarning($"[TechBuildHandler] Unknown tech type for building: {subType}");
        return false;
    }

    private bool HasRequiredBuildResources(Player player, GameObject location)
    {
        if (techToBuild?.BuildCost == null)
        {
            return true;
        }

        foreach (Resource resource in techToBuild.BuildCost)
        {
            int playerAmount = player.GetResourceAmount(location, resource.Type);
            int requiredAmount = resource.Amount;

            if (playerAmount < requiredAmount)
            {
                Debug.Log($"[TechBuildHandler] Insufficient {resource.Type} at {location.name} - has {playerAmount}, needs {requiredAmount}");
                return false;
            }
        }
        return true;
    }

    private bool HasBasicAssembler(Player player, GameObject location)
    {
        List<Module> modules = player.GetModulesOnPlanet(location);
        foreach (Module module in modules)
        {
            if (module.Name == "Assembler Module")
            {
                return true;
            }
        }

        // Universal Builder can also act as assembler
        List<Ship> ships = player.GetShipsAtLocation(location);
        foreach (Ship ship in ships)
        {
            if (ship.Name == "Universal Builder")
            {
                return true;
            }
        }

        return false;
    }

    private bool HasAdvancedAssembler(Player player, GameObject location)
    {
        // Earth has an Advanced Assembler
        if (location.name == "Earth")
        {
            return true;
        }

        // Check player's modules
        List<Module> modules = player.GetModulesOnPlanet(location);

        foreach (Module module in modules)
        {
            if (module.Name == "Advanced Assembler")
            {
                return true;
            }
        }

        return false;
    }

    private void HandleBuildModeClick()
    {
        PlayerCameraController cameraController = FindFirstObjectByType<PlayerCameraController>();
        Camera activeCamera = cameraController?.GetActiveCamera();

        if (activeCamera == null)
        {
            activeCamera = Camera.main;
            if (activeCamera == null)
            {
                activeCamera = FindFirstObjectByType<Camera>();
            }
        }

        if (activeCamera == null)
        {
            Debug.LogError("No camera found for raycast");
            ExitBuildSelectionMode();
            return;
        }

        Ray ray = activeCamera.ScreenPointToRay(Input.mousePosition);
        RaycastHit hit;

        if (Physics.Raycast(ray, out hit))
        {
            GameObject clickedObject = hit.collider.gameObject;

            // Check if clicked object is a glowing world card or celestial body
            if (glowingWorldCards.Contains(clickedObject) || glowingWorlds.Contains(clickedObject))
            {
                BuildTechAtWorld(clickedObject);
                return;
            }
        }

        ExitBuildSelectionMode();
    }

    private void BuildTechAtWorld(GameObject clickedObject)
    {
        GameManager gameManager = GameManager.Instance;
        Player currentPlayer = gameManager.CurrentPlayer;

        if (techToBuild == null || currentPlayer == null)
        {
            ExitBuildSelectionMode();
            return;
        }

        // Determine the actual world location
        GameObject worldLocation = null;

        if (glowingWorldCards.Contains(clickedObject))
        {
            // Clicked on world card - get the celestial body
            WorldCardVisual worldVisual = clickedObject.GetComponent<WorldCardVisual>();
            if (worldVisual != null)
            {
                worldLocation = worldVisual.GetCelestialBody();
            }
        }
        else if (glowingWorlds.Contains(clickedObject))
        {
            // Clicked directly on celestial body
            worldLocation = clickedObject;
        }

        if (worldLocation == null)
        {
            ExitBuildSelectionMode();
            return;
        }

        // Use action
        if (!gameManager.UseAction())
        {
            ExitBuildSelectionMode();
            return;
        }

        // Create undoable action before making changes
        bool isShip = techToBuild.SubType?.ToLower().Contains("ship") == true;
        BuildStructureAction buildAction = new BuildStructureAction(
            techToBuild, techToBuild.BuildCost, currentPlayer.PlayerId, worldLocation, isShip);

        // Deduct resources
        foreach (Resource resource in techToBuild.BuildCost)
        {
            currentPlayer.UseResource(worldLocation, resource.Type, resource.Amount);
        }

        // Create the built item based on type
        CreateBuiltItem(techToBuild, currentPlayer, worldLocation);

        GameLogManager logManager = GameLogManager.Instance;
        if (logManager != null)
        {
            string type = isShip ? "ship" : "module";
            logManager.AddLog($"Player {currentPlayer.PlayerId + 1} built {techToBuild.Name} ({type})");
        }

        // Register the undoable action
        UndoManager undoManager = UndoManager.Instance;
        undoManager?.RegisterAction(buildAction);

        // Update play area
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        if (playAreaManager != null)
        {
            playAreaManager.RefreshPlayerPlayArea(gameManager.CurrentPlayerIndex);
        }

        // Update UI
        GameUI gameUI = FindFirstObjectByType<GameUI>();
        if (gameUI != null)
        {
            gameUI.RefreshUI();
        }

        ExitBuildSelectionMode();
    }

    private void CreateBuiltItem(CardData cardData, Player player, GameObject location)
    {
        string subType = cardData.SubType?.ToLower() ?? "";

        if (subType.Contains("ship"))
        {
            CreateShip(cardData, player, location);
        }
        else if (subType.Contains("module") || subType.Contains("facility") || cardData.Type?.ToLower().Contains("wonder") == true)
        {
            CreateModule(cardData, player, location);
        }
    }

    private void CreateShip(CardData cardData, Player player, GameObject location)
    {
        Ship ship = new Ship
        {
            Name = cardData.Name,
            CurrentLocation = location,
            DeltaVPerFuel = GetFloatFromParams(cardData, "deltaVPerFuel", 4f),
            CargoCapacity = GetIntFromParams(cardData, "cargoCapacity", 1),
            Strength = GetIntFromParams(cardData, "shipStrength", 0),
            IsConsumedOnSurvey = GetBoolFromParams(cardData, "consumedOnSurvey", false)
        };

        player.AddShip(ship);
        player.ShipsInPlay++;

        // Register with planet
        PlanetBody planetBody = location.GetComponent<PlanetBody>();
        if (planetBody != null)
        {
            planetBody.RegisterShipArrival(ship, player);
        }
    }

    private void CreateModule(CardData cardData, Player player, GameObject location)
    {
        Module module = new Module
        {
            Name = cardData.Name,
            Type = GetModuleTypeFromSubType(cardData.SubType),
            PowerOutput = cardData.PowerOutput,
            PowerRequired = cardData.PowerRequired,
            IsWonder = cardData.IsWonder || cardData.Type?.ToLower().Contains("wonder") == true,
            VictoryPointValue = GetVPFromEffect(cardData.Effect),
            ProcessorDescription = cardData.Effect,
            Tier = cardData.Tier
        };

        player.AddModule(location, module);
    }

    private float GetFloatFromParams(CardData cardData, string key, float defaultValue)
    {
        if (cardData.EffectParams != null && cardData.EffectParams.ContainsKey(key))
        {
            return System.Convert.ToSingle(cardData.EffectParams[key]);
        }
        return defaultValue;
    }

    private int GetIntFromParams(CardData cardData, string key, int defaultValue)
    {
        if (cardData.EffectParams != null && cardData.EffectParams.ContainsKey(key))
        {
            return System.Convert.ToInt32(cardData.EffectParams[key]);
        }
        return defaultValue;
    }

    private bool GetBoolFromParams(CardData cardData, string key, bool defaultValue)
    {
        if (cardData.EffectParams != null && cardData.EffectParams.ContainsKey(key))
        {
            return System.Convert.ToBoolean(cardData.EffectParams[key]);
        }
        return defaultValue;
    }

    private ModuleType GetModuleTypeFromSubType(string subType)
    {
        if (string.IsNullOrEmpty(subType))
            return ModuleType.Other;

        string lower = subType.ToLower();
        if (lower.Contains("power"))
            return ModuleType.Power;
        if (lower.Contains("extractor"))
            return ModuleType.Extractor;
        if (lower.Contains("processor"))
            return ModuleType.Processor;
        if (lower.Contains("wonder"))
            return ModuleType.Wonder;

        return ModuleType.Other;
    }

    private int GetVPFromEffect(string effect)
    {
        if (string.IsNullOrEmpty(effect))
            return 0;

        System.Text.RegularExpressions.Match match =
            System.Text.RegularExpressions.Regex.Match(effect, @"(\d+)\s*(?:VP|Victory\s*Points?)",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

        if (match.Success)
        {
            return int.Parse(match.Groups[1].Value);
        }

        return 0;
    }
}
